﻿using System.Text.Json.Serialization;
using OpenAI.ObjectModels;
using Thor.Abstractions.Dtos;

namespace Thor.Abstractions.ObjectModels.ObjectModels.SharedModels;

public record FileResponse : ThorBaseResponse, IOpenAiModels.IId, IOpenAiModels.ICreatedAt
{
    [JsonPropertyName("bytes")] public int? Bytes { get; set; }
    [JsonPropertyName("filename")] public string FileName { get; set; }
    public UploadFilePurposes.UploadFilePurpose PurposeEnum => UploadFilePurposes.ToEnum(Purpose);
    [JsonPropertyName("purpose")] public string Purpose { get; set; }
    [JsonPropertyName("status")] public string Status { get; set; }
    [JsonPropertyName("created_at")] public int CreatedAt { get; set; }
    [JsonPropertyName("id")] public string Id { get; set; }
}