{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "Microsoft.EntityFrameworkCore": "Warning", "System": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithThreadId", "WithProcessId"]}, "AllowedHosts": "*", "Urls": "http://localhost:6500", "ConnectionStrings": {"DefaultConnection": "Data Source=ClaudeCodeProxy.db;Cache=Shared"}, "Jwt": {"Issuer": "https://api.token-ai.cn", "Audience": "https://api.token-ai.cn", "SecretKey": "OJOJoiajosindiojhoijh08)Y*()^&*(%*&!(Hjkbauisgid8g91jhvuyg857$&*^", "ExpirationDays": 7, "RefreshTokenExpirationDays": 7, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true}, "RunMigrationsAtStartup": true, "WindowsService": {"ServiceName": "ClaudeCodeProxyService", "DisplayName": "Claude Code Proxy Service", "Description": "Claude Code Proxy API服务，用于代理Claude AI的API请求"}}