﻿// <auto-generated />
using System;
using ClaudeCodeProxy.EntityFrameworkCore.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace ClaudeCodeProxy.EntityFrameworkCore.Sqlite.Migrations
{
    [DbContext(typeof(SqliteDbContext))]
    partial class SqliteDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.7");

            modelBuilder.Entity("ClaudeCodeProxy.Domain.Accounts", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("AccountType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("shared");

                    b.Property<string>("ApiKey")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ApiUrl")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaudeAiOauth")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("GeminiOauth")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<string>("LastError")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Platform")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(50);

                    b.Property<string>("ProjectId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Proxy")
                        .HasColumnType("TEXT");

                    b.Property<int>("RateLimitDuration")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(60);

                    b.Property<DateTime?>("RateLimitedUntil")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("active");

                    b.Property<string>("SupportedModels")
                        .HasColumnType("TEXT");

                    b.Property<long>("UsageCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("AccountType")
                        .HasDatabaseName("IX_Accounts_AccountType");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Accounts_CreatedAt");

                    b.HasIndex("IsEnabled")
                        .HasDatabaseName("IX_Accounts_IsEnabled");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Accounts_Name");

                    b.HasIndex("Platform")
                        .HasDatabaseName("IX_Accounts_Platform");

                    b.HasIndex("Priority")
                        .HasDatabaseName("IX_Accounts_Priority");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_Accounts_Status");

                    b.HasIndex("Platform", "IsEnabled", "Status")
                        .HasDatabaseName("IX_Accounts_Platform_IsEnabled_Status");

                    b.ToTable("Accounts", (string)null);
                });

            modelBuilder.Entity("ClaudeCodeProxy.Domain.ApiKey", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("AllowedClients")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaudeAccountId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaudeConsoleAccountId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("ConcurrencyLimit")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("DailyCostLimit")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,4)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("DailyCostUsed")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EnableClientRestriction")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<bool>("EnableModelRestriction")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("GeminiAccountId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<string>("KeyValue")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Model")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("MonthlyCostLimit")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("MonthlyCostUsed")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Permissions")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("all");

                    b.Property<int?>("RateLimitRequests")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("RateLimitWindow")
                        .HasColumnType("INTEGER");

                    b.Property<string>("RestrictedModels")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Service")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("claude");

                    b.Property<string>("Tags")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<int?>("TokenLimit")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("TotalCost")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,4)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("TotalCostLimit")
                        .HasColumnType("TEXT");

                    b.Property<long>("TotalUsageCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_ApiKeys_CreatedAt");

                    b.HasIndex("IsEnabled")
                        .HasDatabaseName("IX_ApiKeys_IsEnabled");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_ApiKeys_Name");

                    b.HasIndex("Service")
                        .HasDatabaseName("IX_ApiKeys_Service");

                    b.ToTable("ApiKeys", (string)null);
                });

            modelBuilder.Entity("ClaudeCodeProxy.Domain.ModelPricing", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("CacheReadPrice")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,9)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("CacheWritePrice")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,9)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("USD");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("InputPrice")
                        .HasColumnType("decimal(18,9)");

                    b.Property<bool>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("OutputPrice")
                        .HasColumnType("decimal(18,9)");

                    b.HasKey("Id");

                    b.HasIndex("Currency")
                        .HasDatabaseName("IX_ModelPricings_Currency");

                    b.HasIndex("IsEnabled")
                        .HasDatabaseName("IX_ModelPricings_IsEnabled");

                    b.HasIndex("Model")
                        .IsUnique()
                        .HasDatabaseName("IX_ModelPricings_Model");

                    b.ToTable("ModelPricings", (string)null);
                });

            modelBuilder.Entity("ClaudeCodeProxy.Domain.RequestLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("AccountId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("AccountName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<Guid>("ApiKeyId")
                        .HasColumnType("TEXT");

                    b.Property<string>("ApiKeyName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("CacheCreateTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<int>("CacheReadTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<string>("ClientIp")
                        .HasMaxLength(45)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Cost")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<long?>("DurationMs")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<int?>("HttpStatusCode")
                        .HasColumnType("INTEGER");

                    b.Property<int>("InputTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsStreaming")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<string>("Metadata")
                        .HasColumnType("TEXT");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("OutputTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<string>("Platform")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("claude");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("RequestEndTime")
                        .HasColumnType("TEXT");

                    b.Property<int>("RequestHour")
                        .HasColumnType("INTEGER");

                    b.Property<string>("RequestId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("RequestStartTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("success");

                    b.Property<int>("TotalTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ApiKeyId")
                        .HasDatabaseName("IX_RequestLogs_ApiKeyId");

                    b.HasIndex("Model")
                        .HasDatabaseName("IX_RequestLogs_Model");

                    b.HasIndex("Platform")
                        .HasDatabaseName("IX_RequestLogs_Platform");

                    b.HasIndex("RequestDate")
                        .HasDatabaseName("IX_RequestLogs_RequestDate");

                    b.HasIndex("RequestStartTime")
                        .HasDatabaseName("IX_RequestLogs_RequestStartTime");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_RequestLogs_Status");

                    b.HasIndex("RequestDate", "ApiKeyId")
                        .HasDatabaseName("IX_RequestLogs_RequestDate_ApiKeyId");

                    b.HasIndex("RequestDate", "Model")
                        .HasDatabaseName("IX_RequestLogs_RequestDate_Model");

                    b.HasIndex("RequestStartTime", "RequestHour")
                        .HasDatabaseName("IX_RequestLogs_RequestStartTime_RequestHour");

                    b.ToTable("RequestLogs", (string)null);
                });

            modelBuilder.Entity("ClaudeCodeProxy.Domain.StatisticsSnapshot", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<int?>("ActiveAccountCount")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ActiveApiKeyCount")
                        .HasColumnType("INTEGER");

                    b.Property<Guid?>("ApiKeyId")
                        .HasColumnType("TEXT");

                    b.Property<double?>("AverageResponseTime")
                        .HasColumnType("REAL");

                    b.Property<long>("CacheCreateTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.Property<long>("CacheReadTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<long>("FailedRequestCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.Property<long>("InputTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.Property<long?>("MaxResponseTime")
                        .HasColumnType("INTEGER");

                    b.Property<long?>("MinResponseTime")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Model")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<long>("OutputTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.Property<int?>("RateLimitedAccountCount")
                        .HasColumnType("INTEGER");

                    b.Property<long>("RequestCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.Property<DateTime>("SnapshotDate")
                        .HasColumnType("TEXT");

                    b.Property<int?>("SnapshotHour")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SnapshotType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<long>("SuccessfulRequestCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.Property<decimal>("TotalCost")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(0m);

                    b.Property<long>("TotalTokens")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0L);

                    b.Property<int?>("UniqueUserCount")
                        .HasColumnType("INTEGER");

                    b.Property<long>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(1L);

                    b.HasKey("Id");

                    b.HasIndex("ApiKeyId")
                        .HasDatabaseName("IX_StatisticsSnapshots_ApiKeyId");

                    b.HasIndex("Model")
                        .HasDatabaseName("IX_StatisticsSnapshots_Model");

                    b.HasIndex("SnapshotDate")
                        .HasDatabaseName("IX_StatisticsSnapshots_SnapshotDate");

                    b.HasIndex("SnapshotType")
                        .HasDatabaseName("IX_StatisticsSnapshots_SnapshotType");

                    b.HasIndex("SnapshotType", "SnapshotDate")
                        .HasDatabaseName("IX_StatisticsSnapshots_SnapshotType_SnapshotDate");

                    b.HasIndex("SnapshotType", "SnapshotDate", "ApiKeyId")
                        .HasDatabaseName("IX_StatisticsSnapshots_SnapshotType_SnapshotDate_ApiKeyId");

                    b.HasIndex("SnapshotType", "SnapshotDate", "Model")
                        .HasDatabaseName("IX_StatisticsSnapshots_SnapshotType_SnapshotDate_Model");

                    b.HasIndex("SnapshotType", "SnapshotDate", "SnapshotHour")
                        .HasDatabaseName("IX_StatisticsSnapshots_SnapshotType_SnapshotDate_SnapshotHour");

                    b.ToTable("StatisticsSnapshots", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
