name: 🐛 Bug Report
description: File a bug report to help us improve
title: "[Bug]: "
labels: ["bug", "triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! 
        Please provide as much detail as possible to help us reproduce and fix the issue.

  - type: textarea
    id: description
    attributes:
      label: 📋 Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: 🔄 Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: ✅ Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: What should have happened?
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: ❌ Actual Behavior
      description: What actually happened instead?
      placeholder: What actually happened?
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: 📸 Screenshots
      description: If applicable, add screenshots to help explain your problem.
      placeholder: Drag and drop screenshots here...

  - type: dropdown
    id: environment
    attributes:
      label: 🖥️ Environment
      description: Where are you running ClaudeCodeProxy?
      options:
        - Docker (Linux)
        - Docker (Windows)
        - Docker (macOS)
        - Standalone (Linux)
        - Standalone (Windows)
        - Standalone (macOS)
        - Development Environment
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: 📦 Version
      description: What version of ClaudeCodeProxy are you running?
      placeholder: e.g., v1.0.0
    validations:
      required: true

  - type: dropdown
    id: browsers
    attributes:
      label: 🌐 Browser (if applicable)
      description: Which browser are you using?
      multiple: true
      options:
        - Chrome
        - Firefox
        - Safari
        - Edge
        - Other

  - type: textarea
    id: logs
    attributes:
      label: 📝 Logs
      description: Please copy and paste any relevant log output.
      render: shell
      placeholder: Paste logs here...

  - type: textarea
    id: additional
    attributes:
      label: ℹ️ Additional Context
      description: Add any other context about the problem here.
      placeholder: Any additional information...

  - type: checkboxes
    id: terms
    attributes:
      label: ✅ Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided all the requested information
          required: true
        - label: I am willing to help test a fix for this issue
          required: false