﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{B6C90088-68B7-46FE-ABD9-C919DD44E46A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ClaudeCodeProxy.Host", "src\ClaudeCodeProxy.Host\ClaudeCodeProxy.Host.csproj", "{0F18B94E-7449-4DCA-AD17-746C7965ABA4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{F687CBD4-FD33-47BD-B950-2ED51AA35576}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yaml = docker-compose.yaml
		Directory.Build.props = Directory.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ClaudeCodeProxy.Core", "src\ClaudeCodeProxy.Core\ClaudeCodeProxy.Core.csproj", "{8F2FFD27-22BC-4762-A0F6-A404C1A58202}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ClaudeCodeProxy.Abstraction", "src\ClaudeCodeProxy.Abstraction\ClaudeCodeProxy.Abstraction.csproj", "{A0D0E45E-A558-4FC2-BD31-696E59A99D59}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ClaudeCodeProxy.Domain", "src\ClaudeCodeProxy.Domain\ClaudeCodeProxy.Domain.csproj", "{577AC59A-BEB1-4C83-9685-AFCA3136566D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Provide", "Provide", "{8271D8CE-C39A-49D8-A154-77FC20F27E2C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ClaudeCodeProxy.EntityFrameworkCore.Sqlite", "src\Provide\ClaudeCodeProxy.EntityFrameworkCore.Sqlite\ClaudeCodeProxy.EntityFrameworkCore.Sqlite.csproj", "{08F7CF60-51B5-410C-A658-C3FB090CA5A3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{0F18B94E-7449-4DCA-AD17-746C7965ABA4} = {B6C90088-68B7-46FE-ABD9-C919DD44E46A}
		{8F2FFD27-22BC-4762-A0F6-A404C1A58202} = {B6C90088-68B7-46FE-ABD9-C919DD44E46A}
		{A0D0E45E-A558-4FC2-BD31-696E59A99D59} = {B6C90088-68B7-46FE-ABD9-C919DD44E46A}
		{577AC59A-BEB1-4C83-9685-AFCA3136566D} = {B6C90088-68B7-46FE-ABD9-C919DD44E46A}
		{8271D8CE-C39A-49D8-A154-77FC20F27E2C} = {B6C90088-68B7-46FE-ABD9-C919DD44E46A}
		{08F7CF60-51B5-410C-A658-C3FB090CA5A3} = {8271D8CE-C39A-49D8-A154-77FC20F27E2C}
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0F18B94E-7449-4DCA-AD17-746C7965ABA4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0F18B94E-7449-4DCA-AD17-746C7965ABA4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0F18B94E-7449-4DCA-AD17-746C7965ABA4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0F18B94E-7449-4DCA-AD17-746C7965ABA4}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F2FFD27-22BC-4762-A0F6-A404C1A58202}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F2FFD27-22BC-4762-A0F6-A404C1A58202}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F2FFD27-22BC-4762-A0F6-A404C1A58202}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F2FFD27-22BC-4762-A0F6-A404C1A58202}.Release|Any CPU.Build.0 = Release|Any CPU
		{A0D0E45E-A558-4FC2-BD31-696E59A99D59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A0D0E45E-A558-4FC2-BD31-696E59A99D59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A0D0E45E-A558-4FC2-BD31-696E59A99D59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A0D0E45E-A558-4FC2-BD31-696E59A99D59}.Release|Any CPU.Build.0 = Release|Any CPU
		{577AC59A-BEB1-4C83-9685-AFCA3136566D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{577AC59A-BEB1-4C83-9685-AFCA3136566D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{577AC59A-BEB1-4C83-9685-AFCA3136566D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{577AC59A-BEB1-4C83-9685-AFCA3136566D}.Release|Any CPU.Build.0 = Release|Any CPU
		{08F7CF60-51B5-410C-A658-C3FB090CA5A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{08F7CF60-51B5-410C-A658-C3FB090CA5A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{08F7CF60-51B5-410C-A658-C3FB090CA5A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{08F7CF60-51B5-410C-A658-C3FB090CA5A3}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
