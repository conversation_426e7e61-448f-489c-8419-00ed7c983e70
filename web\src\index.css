@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.13 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.13 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.13 0 0);
  --primary: oklch(0.21 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0 0);
  --secondary-foreground: oklch(0.21 0 0);
  --muted: oklch(0.967 0 0);
  --muted-foreground: oklch(0.551 0 0);
  --accent: oklch(0.967 0 0);
  --accent-foreground: oklch(0.21 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.928 0 0);
  --input: oklch(0.928 0 0);
  --ring: oklch(0.707 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.13 0 0);
  --sidebar-primary: oklch(0.21 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0 0);
  --sidebar-accent-foreground: oklch(0.21 0 0);
  --sidebar-border: oklch(0.928 0 0);
  --sidebar-ring: oklch(0.707 0 0);
}

.dark {
  /* Core background grays - deeper and more consistent */
  --background: oklch(0.08 0.002 0);
  --foreground: oklch(0.96 0.002 0);
  
  /* Surface layers for depth hierarchy */
  --card: oklch(0.11 0.003 0);
  --card-foreground: oklch(0.96 0.002 0);
  --popover: oklch(0.12 0.003 0);
  --popover-foreground: oklch(0.96 0.002 0);
  
  /* Interactive elements - cleaner gray progression */
  --primary: oklch(0.82 0.008 0);
  --primary-foreground: oklch(0.08 0.002 0);
  --secondary: oklch(0.16 0.006 0);
  --secondary-foreground: oklch(0.96 0.002 0);
  --muted: oklch(0.16 0.006 0);
  --muted-foreground: oklch(0.68 0.012 0);
  --accent: oklch(0.19 0.008 0);
  --accent-foreground: oklch(0.96 0.002 0);
  
  /* Status and feedback - subtle gray variations */
  --destructive: oklch(0.45 0.08 0);
  --border: oklch(0.22 0.006 0);
  --input: oklch(0.22 0.006 0);
  --ring: oklch(0.48 0.012 0);
  
  /* Chart colors - vibrant colors for better visibility in dark mode */
  --chart-1: oklch(0.7 0.15 260);  /* Blue */
  --chart-2: oklch(0.65 0.18 142); /* Green */
  --chart-3: oklch(0.68 0.20 60);  /* Orange/Yellow */
  --chart-4: oklch(0.62 0.16 320); /* Purple */
  --chart-5: oklch(0.66 0.17 15);  /* Red */
  
  /* Sidebar - refined gray hierarchy */
  --sidebar: oklch(0.10 0.004 0);
  --sidebar-foreground: oklch(0.94 0.002 0);
  --sidebar-primary: oklch(0.74 0.015 0);
  --sidebar-primary-foreground: oklch(0.08 0.002 0);
  --sidebar-accent: oklch(0.17 0.008 0);
  --sidebar-accent-foreground: oklch(0.94 0.002 0);
  --sidebar-border: oklch(0.20 0.006 0);
  --sidebar-ring: oklch(0.46 0.012 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
