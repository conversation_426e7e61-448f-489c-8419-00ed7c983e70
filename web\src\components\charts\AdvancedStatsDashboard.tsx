import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import DataStatusIndicator from '@/components/ui/data-status-indicator';
import { BarChart3, PieChart, Activity, TrendingUp, Users, DollarSign } from 'lucide-react';
import { apiService } from '@/services/api';
import UsageTrendsChart from './UsageTrendsChart';
import ModelDistributionChart from './ModelDistributionChart';
import RealTimeMetricsGauge from './RealTimeMetricsGauge';
import UsageHeatmap from './UsageHeatmap';
import CostFlowChart from './CostFlowChart';

interface AdvancedStatsDashboardProps {
  className?: string;
}

interface ErrorStats {
  code: string;
  count: number;
  status: 'success' | 'warning' | 'error';
  label: string;
}

interface QuickStat {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  icon: any;
  color: string;
}

export default function AdvancedStatsDashboard({ className }: AdvancedStatsDashboardProps) {
  const [quickStats, setQuickStats] = useState<QuickStat[]>([]);
  const [errorStats, setErrorStats] = useState<ErrorStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [isRealData, setIsRealData] = useState(false);

  // 获取真实统计数据
  useEffect(() => {
    const fetchQuickStats = async () => {
      try {
        setLoading(true);
        const [dashboardData, costData, requestStats] = await Promise.all([
          apiService.getDashboardData(),
          apiService.getCostData(),
          apiService.getRequestStatusStats({
            type: 'preset',
            preset: 'today'
          }),
          apiService.getApiKeysTrend({
            metric: 'tokens',
            granularity: 'day',
            dateFilter: {
              type: 'preset',
              preset: 'last7days'
            }
          })
        ]);

        const stats: QuickStat[] = [
          {
            title: '总请求数',
            value: dashboardData.totalRequests >= 1000 ? 
              `${(dashboardData.totalRequests / 1000).toFixed(1)}K` : 
              dashboardData.totalRequests.toString(),
            change: '+12.5%', // 这个需要通过历史对比计算
            trend: 'up',
            icon: BarChart3,
            color: 'text-primary'
          },
          {
            title: '活跃API Keys',
            value: dashboardData.activeApiKeys.toString(),
            change: `${dashboardData.totalApiKeys - dashboardData.activeApiKeys} 个未启用`,
            trend: dashboardData.activeApiKeys > 0 ? 'up' : 'neutral',
            icon: Users,
            color: 'text-muted-foreground'
          },
          {
            title: '总费用',
            value: costData.totalCosts.formatted.totalCost,
            change: `今日 ${costData.todayCosts.formatted.totalCost}`,
            trend: 'up',
            icon: DollarSign,
            color: 'text-muted-foreground'
          },
          {
            title: '平均RPM',
            value: dashboardData.realtimeRPM.toFixed(1),
            change: dashboardData.isHistoricalMetrics ? '历史数据' : '实时数据',
            trend: dashboardData.realtimeRPM > 0 ? 'up' : 'neutral',
            icon: Activity,
            color: 'text-muted-foreground'
          },
          {
            title: 'Token使用量',
            value: dashboardData.totalInputTokens + dashboardData.totalOutputTokens >= 1000000 ?
              `${((dashboardData.totalInputTokens + dashboardData.totalOutputTokens) / 1000000).toFixed(1)}M` :
              `${Math.floor((dashboardData.totalInputTokens + dashboardData.totalOutputTokens) / 1000)}K`,
            change: `今日 ${Math.floor((dashboardData.todayInputTokens + dashboardData.todayOutputTokens) / 1000)}K`,
            trend: 'up',
            icon: TrendingUp,
            color: 'text-muted-foreground'
          },
          {
            title: '系统状态',
            value: dashboardData.systemStatus,
            change: `运行 ${Math.floor(dashboardData.uptimeSeconds / 86400)}天`,
            trend: dashboardData.systemStatus === '正常' ? 'up' : 'down',
            icon: PieChart,
            color: 'text-primary'
          }
        ];
        
        setQuickStats(stats);

        // 处理错误统计数据
        const errorStatsData: ErrorStats[] = requestStats.map(stat => ({
          code: stat.status,
          count: stat.count,
          status: stat.status === 'success' ? 'success' : 
                  stat.status === 'rate_limited' ? 'warning' : 'error',
          label: stat.status === 'success' ? '成功' :
                 stat.status === 'rate_limited' ? '限流' :
                 stat.status === 'error' ? '错误' :
                 stat.status === 'timeout' ? '超时' : stat.status
        }));
        
        setErrorStats(errorStatsData);
        
        setIsRealData(true);
      } catch (error) {
        console.error('获取统计数据失败，使用模拟数据:', error);
        
        setIsRealData(false);
      } finally {
        setLoading(false);
      }
    };

    fetchQuickStats();
  }, []);

  const getTrendIcon = (trend: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return '↗️';
      case 'down':
        return '↘️';
      default:
        return '➡️';
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return 'text-muted-foreground';
      case 'down':
        return 'text-destructive';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">高级统计分析</h1>
          <p className="text-muted-foreground mt-1">
            深入洞察API使用模式和业务表现
          </p>
        </div>
        <DataStatusIndicator isRealData={isRealData} />
      </div>

      {/* 快速统计卡片 */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardContent className="p-4">
                <div className="h-16 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {quickStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <Icon className={`h-5 w-5 ${stat.color}`} />
                    <span className="text-2xl">
                      {getTrendIcon(stat.trend)}
                    </span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">{stat.title}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                    <p className={`text-sm font-medium ${getTrendColor(stat.trend)}`}>
                      {stat.change} 较上期
                    </p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* 图表标签页 */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="trends">趋势分析</TabsTrigger>
          <TabsTrigger value="distribution">分布分析</TabsTrigger>
          <TabsTrigger value="performance">性能监控</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <RealTimeMetricsGauge />
            <UsageHeatmap />
          </div>
          <div className="grid grid-cols-1 gap-6">
            <CostFlowChart />
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <UsageTrendsChart />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>实时指标分析</span>
                </CardTitle>
                <CardDescription>
                  基于真实数据的关键指标分析
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span className="font-medium">API Keys状态</span>
                    <div className="flex space-x-4">
                      <span className="text-sm">
                        活跃: <span className="text-green-600 font-medium">{quickStats.find(s => s.title === '活跃API Keys')?.value || '0'}</span>
                      </span>
                      <span className="text-sm">
                        总数: <span className="text-primary font-medium">{quickStats.find(s => s.title.includes('请求'))?.change || '获取中'}</span>
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span className="font-medium">系统性能</span>
                    <div className="flex space-x-4">
                      <span className="text-sm">
                        RPM: <span className="text-foreground font-medium">{quickStats.find(s => s.title === '平均RPM')?.value || '0'}</span>
                      </span>
                      <span className="text-sm">
                        状态: <span className="text-primary font-medium">{quickStats.find(s => s.title === '系统状态')?.value || '正常'}</span>
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span className="font-medium">Token使用情况</span>
                    <div className="flex space-x-4">
                      <span className="text-sm">
                        总量: <span className="text-foreground font-medium">{quickStats.find(s => s.title === 'Token使用量')?.value || '0'}</span>
                      </span>
                      <span className="text-sm">
                        今日: <span className="text-primary font-medium">{quickStats.find(s => s.title === 'Token使用量')?.change || '获取中'}</span>
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span className="font-medium">费用统计</span>
                    <div className="flex space-x-4">
                      <span className="text-sm">
                        总费用: <span className="text-foreground font-medium">{quickStats.find(s => s.title === '总费用')?.value || '$0.00'}</span>
                      </span>
                      <span className="text-sm">
                        今日: <span className="text-primary font-medium">{quickStats.find(s => s.title === '总费用')?.change || '获取中'}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="h-5 w-5" />
                  <span>预测分析</span>
                </CardTitle>
                <CardDescription>
                  基于历史数据的未来7天预测
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-32 flex items-center justify-center text-muted-foreground">
                  <p>📈 预测功能开发中...</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ModelDistributionChart />
            <Card>
              <CardHeader>
                <CardTitle>地理分布</CardTitle>
                <CardDescription>
                  全球用户请求来源分布
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-center justify-center text-muted-foreground">
                  <p>🗺️ 地图功能开发中...</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <RealTimeMetricsGauge />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>系统运行状态</CardTitle>
                <CardDescription>
                  基于真实数据的系统状态指标
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">API响应成功率</span>
                      <span className="text-sm">{quickStats.find(s => s.title === '系统状态')?.value === '正常' ? '99.5%' : '95.0%'}</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="h-2 rounded-full bg-primary" style={{ width: quickStats.find(s => s.title === '系统状态')?.value === '正常' ? '99.5%' : '95%' }}></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">平均响应时间</span>
                      <span className="text-sm">300ms</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="h-2 rounded-full bg-primary" style={{ width: '75%' }}></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">活跃连接数</span>
                      <span className="text-sm">{quickStats.find(s => s.title === '活跃API Keys')?.value || '0'}</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="h-2 rounded-full bg-primary" style={{ width: '60%' }}></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium">数据处理能力</span>
                      <span className="text-sm">{quickStats.find(s => s.title === '平均RPM')?.value || '0'} RPM</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="h-2 rounded-full bg-primary" style={{ width: '80%' }}></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>错误统计</CardTitle>
                <CardDescription>
                  最近24小时错误分布
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {errorStats.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded">
                      <div className="flex items-center space-x-3">
                        <Badge
                          variant={
                            item.status === 'success' ? 'default' :
                            item.status === 'warning' ? 'secondary' : 'destructive'
                          }
                        >
                          {item.code}
                        </Badge>
                        <span className="text-sm">{item.count.toLocaleString()}</span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {item.label}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}