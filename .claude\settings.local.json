{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:raw.githubusercontent.com)", "Bash(npm install:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "mcp__http-server__todo_write", "mcp__http-server__deep_thinking", "Bash(dotnet build)", "Bash(dotnet build:*)", "Bash(git add:*)"], "deny": []}}