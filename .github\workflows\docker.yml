name: <PERSON><PERSON> and Push Docker Images
on:
  workflow_dispatch:
  push:
    branches: [ main ]

env:
  NODE_VERSION: '20.x'

jobs:
  build:
    strategy:
      matrix:
        image: [
          { name: 'claude-code-proxy', type: 'backend', dockerfile: 'src/ClaudeCodeProxy.Host/Dockerfile', context: '.' }
        ]
      fail-fast: false
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      
      - name: Build frontend
        working-directory: ./web
        env:
          VITE_OUT_DIR: dist
        run: |
          rm -rf package-lock.json node_modules
          npm i
          npm run build
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login to Docker Registry
        uses: docker/login-action@v3
        with:
          registry: crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      
      - name: Build and push ${{ matrix.image.name }}
        uses: docker/build-push-action@v5
        with:
          context: ${{ matrix.image.context }}
          file: ${{ matrix.image.dockerfile }}
          platforms: linux/arm64,linux/amd64
          push: true
          tags: crpi-j9ha7sxwhatgtvj4.cn-shenzhen.personal.cr.aliyuncs.com/koala-ai/${{ matrix.image.name }}