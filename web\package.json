{"name": "web", "private": true, "version": "0.1.5", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:prod": "cross-env VITE_OUT_DIR=../src/ClaudeCodeProxy.Host/wwwroot tsc -b && vite build && echo 前端构建完成，文件已输出到后端wwwroot目录", "build:ci": "cross-env VITE_OUT_DIR=dist tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@types/recharts": "^1.8.29", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.535.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/vite": "^4.0.3", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "cross-env": "^7.0.3", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}